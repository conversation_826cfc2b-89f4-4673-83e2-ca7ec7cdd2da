<template>
  <div>
    <!-- 微信授权登录处理中的加载状态 -->
    <div v-if="isProcessingWxLogin" class="wx-login-loading">
      <div class="loading-content">
        <div class="loading-spinner"></div>
        <p>正在处理微信登录...</p>
      </div>
    </div>

    <div v-show="!!size('m') && !isProcessingWxLogin" class="container">
      <!-- <van-button @click="changColorBlue" type="primary">蓝色#2F92EE</van-button>
          <van-button @click="changColorDefault" type="primary">紫色#8A4CFF</van-button>
          <van-button @click="changColorOrange" type="primary">橙色</van-button> -->

      <!-- <div v-if="$store.state.projectData.defaultHome && $store.state.projectData.defaultHome === '/defaultHome'"> -->
      <div
        class="newbgImg"
        v-if="$store.state.projectData.defaultHome === '/defaultHome'"
      >
        <!-- <p class="new-welcome-font">MedSci SCRM</p>
        <p class="new-welcome-font-h2">赋能数字化学术营销</p>  -->
      </div>
      <div v-else-if="backImage" class="bg-height bgColor">
        <!-- <img src="https://static.medsci.cn/public-image/ms-image/b5db7670-790c-11ec-92bc-af0e6edd4208_kv.png" alt=""> -->
        <img :src="backImage" alt="" />
      </div>

      <div v-else class="bgColor bgImg">
        <p class="welcome-font">WELCOME</p>
        <p class="welcome-font-h2">医讯达</p>
      </div>
      <div class="content">
        <h2 class="title">手机验证码登录</h2>
        <span class="tip">未注册的手机号验证成功后会为您自动创建账户</span>
        <div class="radiusSty mobileSty">
          <svg-icon icon-class="mobile" class="iconSty" />
          <van-field
            type="tel"
            v-model.number="phoneNumber"
            placeholder="请输入手机号"
            :clearable="true"
            maxlength="11"
          >
          </van-field>
        </div>

        <div class="flexSty">
          <div class="radiusSty mobileSty" style="width: 60%; margin-right: 3%">
            <svg-icon icon-class="verificationCode" class="iconSty" />
            <van-field
              v-model="validCode"
              maxlength="6"
              placeholder="请输入验证码"
            />
          </div>
          <van-button
            plain
            type="info"
            style="flex: 1; vertical-align: middle"
            class="radiusStyBtn txtSize"
            :class="{ 'disabled-valid-btn': validActive }"
            @click="getValidCode"
          >
            <span v-show="!validActive" class="fnColor">{{ getText }}</span>
            <span v-show="validActive">已发送 {{ validBtnSecond }}s</span>
          </van-button>
        </div>

        <div class="nc-wrap" ref="ncWrap" v-if="showSlideDom" :key="slideKey">
          <div id="nc"></div>
        </div>

        <div class="txtSize must-check">
          <van-checkbox v-model="checked" icon-size="16px"></van-checkbox>
          <p class="agree-text">
            我已阅读并同意<span
              @click="openDetail('agreement')"
              class="fontColor user-xy"
              >《用户协议》</span
            >和<span @click="openDetail('secret')" class="fontColor user-xy"
              >《隐私条款》</span
            >
          </p>
        </div>

        <van-button
          @click="handleLogin(false)"
          :disabled="disabled"
          :class="{
            'btnSty disabled-btn': disabled,
            'btnSty bgColor active-btn': !disabled,
          }"
          >登录</van-button
        >
        <!-- <div class="wxLogin">
          <img
            src="https://static.medsci.cn/public-image/ms-image/8fe45f10-8d72-11ed-b66b-937b834e3ef9_编组 <EMAIL>"
            alt=""
            v-show="
              appid &&
              ($store.state.projectData.disparkState === 1 ||
                $store.state.projectData.disparkState === 2)
            "
            class="wxAuthorization"
            @click="wxAuthorization"
          />
        </div> -->
      </div>
    </div>

    <PcLogin v-show="!!size('pc') && !isProcessingWxLogin" />
  </div>
</template>

<script>
import Mixin from "@/mixins/login/index";

export default {
  name: "Login",
  head() {
    return {
      title: this.globalTitle ? `登录-${this.globalTitle}` : "",
    };
  },


  data() {
    return {
      appid: "",
      //  url:'https://yxd.medsci.cn/login'
      // url:'https://out.s.medsci.cn'
      // url:'/Wxauthorization'
      url: "/login",
      // 微信登录处理状态
      isProcessingWxLogin: false,
    };
  },
  computed: {
    backImage() {
      return (
        this.$store.state.projectData.h5BackgroundUrl ||
        "https://static.medsci.cn/public-image/ms-image/b1ca9b60-59a3-11ed-b66b-937b834e3ef9_default.png"
      );
    },
  },
  methods: {
    // async wxAuthorization() {
    //   let res = await axios.get('https://open.weixin.qq.com/connect/oauth2/authorize',{
    //     params:{
    //         appid:this.appid,
    //         redirect_uri:encodeURIComponent(this.url),
    //         response_type:'code',
    //         scope:'snsapi_userinfo',
    //         state:'STATE#wechat_redirect'
    //     }
    //   })
    // },
    // encodeURIComponent(this.url)
    wxAuthorization() {
      // console.log(1);
      // this.code = this.getCode('code')
      window.location.href =
        "https://open.weixin.qq.com/connect/oauth2/authorize?appid=" +
        this.appid +
        "&redirect_uri=" +
        encodeURIComponent(window.location.origin + this.url) +
        "&response_type=code&scope=snsapi_userinfo&state=STATE#wechat_redirect";
    },
    // getCode(value){
    //    let reg = new RegExp("(^|&)"+ value +"=([^&]*)(&|$)");
    //    let r = window.location.search.substr(1).match(reg)
    //    if(r != null){
    //     return unescape(r[2])
    //    }else{
    //     return null
    //    }
    // },
    async getWxConfig() {
      const res = await this.$api.user.getWxConfig();
      if (res.data) {
        this.appid = res.data.appid;
      }
    },

    // 检查是否需要执行微信授权
    checkWxAuthorization() {
      // 判断是否在微信环境中（非企业微信）
      const isInWeixin = this.globalIsWx();

      // 检查条件：appid存在 && disparkState为1或2 && 在微信环境（非企业微信）
      if (this.appid &&
          (this.$store.state.projectData.disparkState === 1 ||
           this.$store.state.projectData.disparkState === 2) &&
          isInWeixin) {
        // 从当前URL获取code参数，避免#号影响
        const code = this.getCodeFromUrl();
        if (!code || code == '0') {
          // 直接调用微信授权
          this.wxAuthorization();
        }
      }
    },

    // 从当前URL获取code参数
    getCodeFromUrl() {
      if (!process.client) return null;

      const url = window.location.href;
      // 使用更灵活的正则表达式匹配code参数，支持各种格式
      // 支持: ?code=xxx, &code=xxx, #/?code=xxx, #/&code=xxx 等格式
      const match = url.match(/[?&#/]code=([^&#]*)/);
      return match ? match[1] : null;
    },


  },
  beforeMount() {
    // 在微信环境下，如果有授权code且未登录，显示加载状态
    if (process.client) {
      const isWeixinBrowser = this.globalIsWx();
      const urlParams = new URLSearchParams(window.location.search);
      const hasWxCode = urlParams.get('code');
      const isLoggedIn = this.globalIsLogin();

      console.log('微信登录检测:', {
        hasWxCode,
        isLoggedIn,
        isWeixinBrowser
      });

      // 只有在微信环境下才显示加载状态
      if (isWeixinBrowser && hasWxCode && !isLoggedIn) {
        console.log('显示微信登录加载状态');
        this.isProcessingWxLogin = true;
      }
    }
  },

  async mounted() {
    try {
      await this.getWxConfig();

      // 优化：先处理微信授权登录，避免闪现登录页
      const hasHandledWxLogin = await this.wxSignLogin();

      console.log('wxSignLogin 处理结果:', hasHandledWxLogin);

      // 如果处理了微信登录，不需要显示登录页面
      if (hasHandledWxLogin) {
        console.log('微信登录处理成功，不显示登录页面');
        return;
      }

      // 隐藏加载状态，显示登录页面
      console.log('显示登录页面');
      this.isProcessingWxLogin = false;

      // 页面加载时执行微信授权判断逻辑
      this.checkWxAuthorization();

      // 加载验证码
      this.loadTencentCaptcha();
    } catch (error) {
      console.error('登录页面初始化失败:', error);
      // 确保在异常情况下也能显示登录页面
      this.isProcessingWxLogin = false;
      this.loadTencentCaptcha();
    }
  },



  mixins: [Mixin],
};
</script>
<style src="./../styles/index.scss" scoped lang="scss"></style>
<style lang="scss" scoped>
.wx-login-loading {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100vh;
  background: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;

  .loading-content {
    text-align: center;

    .loading-spinner {
      width: 40px;
      height: 40px;
      border: 3px solid #f3f3f3;
      border-top: 3px solid #2f92ee;
      border-radius: 50%;
      animation: spin 1s linear infinite;
      margin: 0 auto 16px;
    }

    p {
      color: #666;
      font-size: 14px;
      margin: 0;
    }
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.wxLogin {
  margin-top: 50px;
}
.container {
  min-height: 100vh;
  background: #fff;
  padding-bottom: 3rem;
}

/*.fontColor {*/
/*  color: #2f92ee;*/
/*}*/

.txtSize {
  margin-top: 0.4rem;
  font-size: 0.24rem;
}

.must-check {
  display: flex;
  &::v-deep .van-checkbox__icon--checked .van-icon {
    background-color: var(--button-color-theme) !important;
    border-color: var(--button-color-theme) !important;
  }
}
.agree-text {
  margin-left: 0.12rem;
  font-size: 0.24rem;
  color: #999;
}
.user-xy {
  color: var(--button-color-theme);
}

.content {
  position: relative;
  border: 1 solid #fff;
  margin: -0.4rem auto 0;
  background: #fff;
  border-top-left-radius: 0.4rem !important;
  border-top-right-radius: 0.4rem !important;
  z-index: 1001;
  box-sizing: border-box;
  padding: 0.56rem 0.6rem 0;

  &::v-deep {
    .van-field {
      padding-left: 0.16rem;
    }
  }
  .title {
    font-size: 0.45rem;
    font-family: PingFangSC-Medium ,sans-serif;
    font-weight: 600;
    color: #333333;
    margin-bottom: 0.25em;
  }
  .wxAuthorization {
    margin-top: 10px;
    text-align: center;
  }
}

.welcome-font {
  /* font-family: "PingFangSCSemibold"; */
  font-size: 0.8rem;
  color: #fff;
}
.new-welcome-font {
  padding-top: 1.27rem;
  color: #fff;
  text-align: center;
  font-size: 0.6rem;
}

.welcome-font-h2 {
  font-family: "PingFangSC" ,sans-serif;
  font-size: 0.56rem;
  color: #fff;
  font-weight: 600;
}
.new-welcome-font-h2 {
  color: #fff;
  font-size: 0.56rem;
  font-weight: 600;
  text-align: center;
}

.bgImg {
  background-image: url("../assets/images/welcomeBg.png");
  background-position: center center;
  background-repeat: no-repeat;
  background-size: cover;
  height: 6rem;
  width: 100%;
  box-sizing: border-box;
  padding-top: 1.77rem;
  padding-left: 0.75rem;
}

.newbgImg {
  background-image: url("https://static.medsci.cn/public-image/ms-image/3ecf8aa0-8d7d-11ec-bca5-7f892b5df5d6_bgc.png");
  background-position: center center;
  background-repeat: no-repeat;
  background-size: 100% 100%;
  height: 4rem;
  width: 100%;
  box-sizing: border-box;
}

.tip {
  font-size: 0.24rem;
  color: #999;
  line-height: 0.33rem;
}

.title {
  font-size: 0.44rem;
  color: #333;
  line-height: 0.62rem;
  font-weight: 500;
}
.radiusSty {
  border: 1px solid #d8d8d8;
  border-radius: 0.12rem;
  margin-top: 0.4rem;
  height: 0.8rem;
}

.mobileSty {
  display: flex;
  align-items: center;
  overflow: hidden;
}

.iconSty {
  margin-left: 0.26rem;
  font-size: 0.48rem;
  /* width: .4rem;
  height: .4rem; */
}

.flexSty {
  display: flex;
  justify-content: space-around;
  align-items: flex-end;
  margin-bottom: 0.25rem;
}

.btnSty {
  width: 100%;
  height: 0.8rem;
  opacity: 1;
  border-radius: 0.12rem;
  margin-top: 0.4rem;
  font-size: 0.34rem;
}

.disabled-btn {
  background: #eee;
  color: #ccc;
}
.active-btn {
  color: #fff;
}

.radiusStyBtn {
  border-radius: 0.12rem;
  margin-top: 0.4rem;
  height: 0.8rem;
  border: 1px solid var(--button-color-theme) !important;
}
.disabled-valid-btn {
  background-color: #eee;
  color: #999;
  border: none !important;
  &::v-deep {
    .van-button__text {
      color: #999;
    }
  }
}

.nc-wrap {
  height: 32px;
  width: 100%;
  margin-top: 0.4rem;
  ::v-deep .nc-container {
    .nc_scale {
      span {
        height: 34px;
      }
    }
  }
}
#nc {
  width: 100%;
}
.fnColor {
  color: var(--button-color-theme);
}
.bgColor {
  background-color: var(--button-color-theme);
}
</style>
